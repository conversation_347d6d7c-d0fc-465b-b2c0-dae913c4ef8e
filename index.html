<!DOCTYPE html>
<html lang="en">
 <head>
   <meta charset="UTF-8" >
   <link rel="apple-touch-icon" sizes="180x180" href="/favicon.png" >
   <link rel="icon" type="image/png" sizes="32x32" href="/favicon.png" >
   <link rel="icon" type="image/png" sizes="16x16" href="/favicon.png" >
   <link rel="preload" href="src/components/ui-kit/icons/images/logoGif.gif" as="image" >
   <!-- <link
     rel="preload"
     fetchpriority="high"
     href="src/components/ui-kit/icons/banner/banner.webp"
     as="image"
     type="image/webp"
   />
   <link
     rel="preload"
     fetchpriority="high"
     href="src/components/ui-kit/icons/banner/Affiliate-web.webp"
     as="image"
     type="image/webp"
   /> -->
   <link
     rel="preload"
     fetchpriority="high"
     href="src/components/ui-kit/icons/brand/brand-logo.webp"
     as="image"
     type="image/webp"
   >
   <link
     rel="preload"
     fetchpriority="high"
     href="src/components/ui-kit/icons/brand/brand-logo-mob.webp"
     as="image"
     type="image/webp"
   >
   <link rel="dns-prefetch" href="//www.google.com" >
   <link rel="preconnect" href="https://www.google.com" crossorigin >
   <link rel="dns-prefetch" href="//www.gstatic.com" >
   <link rel="preconnect" href="https://www.gstatic.com" crossorigin >
   <!-- begin Convert Experiences code-->
   <script defer src="//cdn-4.convertexperiments.com/js/10041529-100412498.js"></script>
   <!-- end Convert Experiences code -->
   <!-- Google Tag Manager -->
   <!-- <script>
     ;(function (w, d, s, l, i) {
       w[l] = w[l] || []
       w[l].push({
         'gtm.start': new Date().getTime(),
         event: 'gtm.js'
       })
       var f = d.getElementsByTagName(s)[0],
         j = d.createElement(s),
         dl = l != 'dataLayer' ? '&l=' + l : ''
       j.async = true
       j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl
       f.parentNode.insertBefore(j, f)
     })(window, document, 'script', 'dataLayer', 'GTM-PR87F2SP')
   </script> -->
   <!-- End Google Tag Manager -->

   <!-- Google tag (gtag.js) -->
   <!-- <script>
     window.dataLayer = window.dataLayer || []
     function gtag() {
       dataLayer.push(arguments)
     }
     gtag('js', new Date())
     gtag('config', 'GTM-PR87F2SP')
   </script> -->
   <!-- End Google tag (gtag.js) -->

   <link rel="manifest" href="/site.webmanifest" >
   <link rel="stylesheet" href="../../../src/App.css" >
   <meta name="msapplication-TileColor" content="#da532c" >
   <!-- <script src="https://polyfill.io/v3/polyfill.min.js?features=default"></script> -->
   <!-- Google Fonts START -->
   <link
     rel="preload"
     href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap"
     as="style"
     onload="this.onload=null;this.rel='stylesheet'"
   >
   <noscript>
     <link
       href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap"
       rel="stylesheet"
       crossorigin
     >
   </noscript>
   <!-- Google Fonts END -->
   <meta name="theme-color" content="#000" >
   <meta name="viewport" content="width=device-width, initial-scale=1.0" >
   <meta
     name="google-signin-client_id"
     content="741935459157-k6l5uprafg7stsoqte0ao43e0mc5r4a6.apps.googleusercontent.com"
   >
   <meta name="facebook-domain-verification" content="6kd3u6l5vu5594s6xrvmefmusgunck" >

   <meta name="robots" content="index">
   <script
     src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAT8hfp5PzEgfvi0sBCMY699-JDYOG-RLA&loading=async&libraries=places&v=weekly"
     defer
   ></script>
   <script
     async
     src="https://sdk.optimove.net/websdk/?tenant_id=1381&tenant_token=464f15d45c964be1aedd13549958c650"
     data-optimove-service-worker-path="/optimove-worker.js"
   ></script>

   <!-- <script src="https://stg-cdn.geocomply.com/274/gc-html5.js" type="text/javascript"></script> -->
   <!-- <script src="https://cdn.geocomply.com/274/gc-html5.js" type="text/javascript"></script>   -->

   <!-- TrustBox script -->
   <script  src="//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js" async></script>
   <!-- End TrustBox script -->
   <!-- Seon Script-->
   <!-- <script defer src="https://cdn.seonintelligence.com/js/v6/agent.umd.js"></script>  -->
   <!-- End Seon script -->
   <!-- JSON-LD script for Organization -->
   <script type="application/ld+json">
     {
       "@context": "https://schema.org",
       "@type": "Organization",
       "name": "The Money Factory",
       "url": "https://www.themoneyfactory.com/",
       "logo": "https://www.themoneyfactory.com/assets/Loading-e366925a.gif",
       "sameAs": [
         "https://x.com/TMFcasino?s=03",
         "https://www.instagram.com/themoneyfactory/?igsh=OTA0NjFscDMyZ3gz&utm_source=qr",
         "https://www.facebook.com/themoneyfactorycasino?mibextid=LQQJ4d",
         "https://t.me/TheMoneyFactoryUS",
         "https://www.reddit.com/user/The_Money_Factory/",
         "https://www.tiktok.com/@themoneyfactorycasino",
         "https://www.snapchat.com/add/tmfcasino"
       ]
     }
   </script>
   <!-- <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script> -->
   <style>
    .loader-wrapper:before {
      position: absolute;
      right: 0;
      width: 100%;
      height: 100%;
      /* background: url('../src/components/ui-kit/icons/png/body-light.png'); */
      background-repeat: no-repeat;
      content: '';
      background-size: cover;
    }
 
    input::-webkit-input-placeholder {
      color: #636366 !important;
      font-size: 14px !important;
      font-weight: 400 !important;
      opacity: 1 !important;
    }
 
    .loader-wrapper {
      position: fixed;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100vw;
      left: 0;
      top: 0;
      overflow: hidden;
      background: rgb(9 10 29 / 13%);
      backdrop-filter: blur(14px);
      z-index: 1000;
    }
 
    .payment-loader-wrapper {
      position: fixed;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100vw;
      left: 0;
      top: 0;
      overflow: hidden;
      background: rgb(9 10 29 / 13%);
      backdrop-filter: blur(14px);
      z-index: 1000;
    }
 
    input:-webkit-autofill,
    input:-webkit-autofill,
    input:-webkit-autofill:focus {
      box-shadow: 0 0 0 2rem #3f3f3f inset !important;
      -webkit-text-fill-color: #fff;
      border: 1px solid #ff964e;
      caret-color: #fff;
      font-size: 14px;
    }
  </style>
   </head>


  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    <script src="https://hosted.paysafe.com/js/v1/latest/paysafe.min.js" async></script>
    <script
      src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"
      defer
    ></script>

   <!-- <script>
     window.intercomSettings = {
       app_id: 'truve12n',
       alignment: 'left',
       horizontal_padding: '20',
       vertical_padding: '50',
       hide_default_launcher: true
     }

     var w = window
     var ic = w.Intercom

     if (typeof ic === 'function') {
       ic('reattach_activator')
       ic('update', w.intercomSettings)
     } else {
       var d = document
       var i = function () {
         i.c(arguments)
       }
       i.q = []
       i.c = function (args) {
         i.q.push(args)
       }
       w.Intercom = i
       var l = function () {
         var s = d.createElement('script')
         s.type = 'text/javascript'
         s.defer = true
         s.src = 'https://widget.intercom.io/widget/truve12n'
         var x = d.getElementsByTagName('script')[0]
         x.parentNode.insertBefore(s, x)
       }
       l()
     }

     // Clean up Intercom on page unload
     window.addEventListener('pagehide', () => {
       if (window.Intercom) {
         window.Intercom('shutdown')
       }
     })

     document.addEventListener('DOMContentLoaded', () => {
       // Increment tabsOpen when the page loads
       const tabsOpen = localStorage.getItem('tabsOpen')
       if (!tabsOpen) {
         localStorage.setItem('tabsOpen', 1)
       } else {
         localStorage.setItem('tabsOpen', parseInt(tabsOpen) + 1)
       }

       // Handle tab closure using the Page Visibility API
       const handleVisibilityChange = () => {
         if (document.visibilityState === 'hidden') {
           const tabsCount = localStorage.getItem('tabsOpen')
           if (tabsCount) {
             localStorage.setItem('tabsOpen', Math.max(0, parseInt(tabsCount) - 1))
           }
         }
       }

       // Attach event listener for visibility changes
       document.addEventListener('visibilitychange', handleVisibilityChange)

       // Cleanup logic for browsers that don't call `visibilitychange` on tab close
       window.addEventListener('beforeunload', () => {
         const tabsCount = localStorage.getItem('tabsOpen')
         if (tabsCount) {
           localStorage.setItem('tabsOpen', Math.max(0, parseInt(tabsCount) - 1))
         }
       })
     })
   </script> -->
   <!-- Commenting captain-up for now -->
   <!-- <div id="_captain_up"></div>
 <script src="https://captainup.com/assets/sdk.js" ></script>
 <script src="https://captainup.com/assets/widget.js" ></script>
 --></body>
</html>


import React, { useRef, useState, useEffect ,lazy, Suspense} from 'react'
import ScratchCard from 'react-scratchcard-v2'
import { ScratchCardImg } from '../ui-kit/icons/webp'
import useScratchCardStyles from './style.js'
import { Typography } from '@mui/material'
import { GeneralQuery, useGetProfileMutation } from '../../reactQuery/index.js'
import { usePortalStore, useUserStore } from '../../store/store.js'
import CloseIcon from '@mui/icons-material/Close'
import { useGetFreeSpinMutation } from '../../reactQuery/bonusQuery.js'
const FreeSpinModal = lazy(() => import('../FreeSpinModal/FreeSpinModal'));
import { formatPriceWithCommas } from '../../utils/helpers.js'

const ScratchCardModal = ({ scratchCardBonus, userBonusId , rewardType, parentMessage, childMessage }) => {
  const classes = useScratchCardStyles()
  const cardRef = useRef(null)
  const [cardSize, setCardSize] = useState({ width: 300, height: 250 })

  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const portalStore = usePortalStore((state) => state)
   const mutationGetFreeSpin = useGetFreeSpinMutation ({
    onSuccess: (res) => {   
         portalStore.openPortal(
      () => (
        <Suspense fallback={<div>Loading...</div>}>
          <FreeSpinModal data={res?.data?.freeSpinBonus} />
        </Suspense>
      ),
      'freeSpinModal'
    ); 
    },
    onError: (error) => {
      console.log(error)
    }
  })
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
       if (res?.data?.data?.isFreeSpinBonusApplicable) {
        mutationGetFreeSpin.mutate()
      }
      else{
             setTimeout(() => portalStore.closePortal(), 1000)
      }
    }
  })

  const successToggler = () => {
   
    getProfileMutation.mutate()
  }

  const errorToggler = () => {
    portalStore.closePortal()
  }

  const { mutate: claimScratchCard, isLoading: isSubmitFormLoading } = GeneralQuery.useClaimScratchCardMutation({
    successToggler,
    errorToggler
  })

  const handleClaimScratchCard = () => {
    claimScratchCard({ userBonusId , rewardType})
  }

  // Resize ScratchCard to container
  useEffect(() => {
    const updateSize = () => {
      if (cardRef.current) {
        const width = Math.min(cardRef.current.offsetWidth, 300)
        const height = (width / 300) * 250 // maintain 300:250 ratio
        setCardSize({ width, height })
      }
    }

    updateSize()
    window.addEventListener('resize', updateSize)
    return () => window.removeEventListener('resize', updateSize)
  }, [])

  const cardSettings = {
    width: cardSize.width,
    height: cardSize.height,
    image: ScratchCardImg,
    finishPercent: 50,
    onComplete: handleClaimScratchCard
  }
  const handleClose = () => {
    portalStore.closePortal();
  }
  return (
    <div className={classes.modalWrapper}>
      <CloseIcon className='close-icon' onClick={handleClose}/>

      <Typography variant='h2' className={classes.heading}>
        Scratch & Win!
      </Typography>

      <div className={classes.cardWrapper} ref={cardRef}>
        <div className={classes.scratchCardRoot}>
          <ScratchCard {...cardSettings} canvasClassName={classes.canvasFix}>
            <div className={classes.rewardText}>Won {formatPriceWithCommas(scratchCardBonus)}{rewardType}</div>
          </ScratchCard>
        </div>
      </div>

      <Typography variant='h4'>Scratch the card to reveal your reward.</Typography>
      <Typography variant='body1'>Your reward will be credited to your balance within 48 hours.</Typography>
    </div>
  )
}

export default ScratchCardModal

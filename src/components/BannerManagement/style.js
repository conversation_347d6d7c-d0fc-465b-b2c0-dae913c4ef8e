import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  bannerLobbySlider: {
    position: 'relative',
    '& .lobby-banner-wrap': {
      marginBottom: theme.spacing(0.625),
      [theme.breakpoints.down('sm')]: {
        marginBottom: theme.spacing(0.313)
      },
      '& .swiper-slide-active': {
        boxShadow: '0px 4px 54px 0px rgba(0, 0, 0, 0.80)',
        [theme.breakpoints.down(575)]: {
          textAlign: 'center'
        },
        [theme.breakpoints.down(436)]: {
          background: 'none !important'
        }
      },
      '& .mySwiper': {
        position: 'relative',
        width: '100%',
        // aspectRatio: '15/4',
        // [theme.breakpoints.down('sm')]: {
        //   aspectRatio: '375/161'
        // },
        overflow: 'hidden',
        borderRadius: theme.spacing(1)
      },
      '& .lobby-slider-section': {
        position: 'relative',
        width: '100%',
        height: '100%'
      }
    },
    bannerSlideContainer: {
      position: 'relative',
      width: '100%',
      height: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      '& > div': {
        width: '100%'
      }
    },
    '& .defaultBannerContainer': {
      position: 'relative',
      width: '100%',
      aspectRatio: '15/4',
      [theme.breakpoints.down('sm')]: {
        aspectRatio: '375/161'
      },
      overflow: 'hidden',
      borderRadius: theme.spacing(1),
      backgroundColor: theme.palette.grey[100]
    },
    '& button': {
      zIndex: 3,
      border: 'none',
      background: 'rgba(255, 255, 255, 0.18)',
      backdropFilter: 'blur(90px)',
      borderRadius: '3rem',
      display: 'flex',
      cursor: 'pointer',
      justifyContent: 'center',
      alignItems: 'center',
      color: '#fff',
      padding: '0.5rem',
      transition: 'background 0.2s ease',
      '&:hover': {
        background: 'rgba(255, 255, 255, 0.33)'
      },
      [theme.breakpoints.down('md')]: {
        width: '30px',
        height: '30px',
        '& svg': {
          width: '15px'
        }
      }
    },
    '& .banner-prev-btn': {
      position: 'absolute',
      transform: 'translate(50%, -50%)',
      top: '50%',
      left: 0,
      [theme.breakpoints.down('md')]: {
        transform: 'translate(25%, -50%)'
      },
      '& svg': {
        position: 'relative',
        left: '4px',
        [theme.breakpoints.down('md')]: {
          left: 0
        }
      }
    },
    '& .banner-next-btn': {
      position: 'absolute',
      top: '50%',
      right: 0,
      transform: 'translate(-50%, -50%)',
      [theme.breakpoints.down('md')]: {
        transform: 'translate(-10%, -50%)'
      }
    }
  }
}))

import { Accordion, AccordionDetails, AccordionSummary, Typography } from '@mui/material'
import { Add, Remove } from '@mui/icons-material'
import { useState } from 'react'
import useStyles from './styles'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'

const LobbyFaq = () => {
  const [expanded, setExpanded] = useState(1)
  const classes = useStyles()

  const handleChange = (panelId) => (event, isExpanded) => {
    setExpanded(isExpanded ? panelId : false)
  }
  return (
    <section className={classes.LobbyFaq}>
      <h1 className={classes.H1Text}>
        <Typography variant='span'>Welcome to</Typography> The Money Factory Casino{' '}
        <Typography variant='span'> Lobby</Typography>
      </h1>
      <h2 className={classes.H2Text}>
        {' '}
        <Typography variant='span'> Frequently</Typography> Ask Questions
      </h2>
      <section>
        <Accordion expanded={expanded === 'panel1'} onChange={handleChange('panel1')}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant='h2'>1. What are SC Coins?</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography>
              SC Coins are virtual sweepstakes tokens you earn by playing games, completing challenges, and
              participating in promotions. They can be used to redeem exclusive rewards and unlock special features.
            </Typography>
          </AccordionDetails>
        </Accordion>
        <Accordion expanded={expanded === 'panel2'} onChange={handleChange('panel2')}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant='h2'>2. Are there any free bonuses or promotions?</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography>
              Yes! We regularly run free spins, sweepstakes, referral bonuses, and seasonal promotions. Check the
              "Promotions" tab often so you don't miss out.
            </Typography>
          </AccordionDetails>
        </Accordion>
        <Accordion expanded={expanded === 'panel3'} onChange={handleChange('panel3')}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant='h2'>3. Can I invite friends to join?</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography>
              Absolutely. Use the "Refer a Friend" feature to invite others. When your friends sign up and play, you
              both can earn bonus rewards.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </section>
    </section>
  )
}

export default LobbyFaq
